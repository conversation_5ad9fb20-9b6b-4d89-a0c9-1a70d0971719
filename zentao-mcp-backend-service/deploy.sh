#!/bin/bash
# ============================================================================
# Zentao MCP Backend Service 部署脚本
# 支持 dev/test/prod 三种环境的独立部署
# ============================================================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SERVICE_DIR="$SCRIPT_DIR"
CONFIG_DIR="$SERVICE_DIR/config"

# 加载公共函数库
if [[ -f "$PROJECT_ROOT/scripts/common.sh" ]]; then
    source "$PROJECT_ROOT/scripts/common.sh"
else
    echo "错误: 找不到公共函数库 $PROJECT_ROOT/scripts/common.sh"
    exit 1
fi

# 显示帮助信息
show_help() {
    cat << 'EOF'
Zentao MCP Backend Service 部署脚本

用法: ./deploy.sh [选项] <环境> [动作]

环境:
  dev       开发环境 (默认) - SQLite数据库，开发配置
  test      测试环境 - SQLite数据库，测试配置
  prod      生产环境 - PostgreSQL数据库，生产配置

动作:
  deploy    构建并部署服务 (默认)
  build     仅构建镜像
  start     启动已存在的服务
  stop      停止服务
  restart   重启服务
  logs      查看服务日志
  status    查看服务状态
  clean     清理环境（停止并删除容器、镜像、卷）
  health    检查服务健康状态
  init      初始化数据库和管理员账户

选项:
  -e, --engine ENGINE    指定容器引擎 (docker|podman)
  -f, --force           强制重新构建镜像
  -v, --verbose         详细输出模式
  --no-cache            构建时不使用缓存
  --pull                构建前拉取最新基础镜像
  --quiet               静默模式，不显示进度指示器
  -h, --help            显示帮助信息

示例:
  ./deploy.sh dev                    # 部署开发环境
  ./deploy.sh prod --engine=podman   # 使用Podman部署生产环境
  ./deploy.sh test build --force     # 强制重新构建测试环境
  ./deploy.sh dev logs               # 查看开发环境日志
  ./deploy.sh prod clean             # 清理生产环境

环境说明:
  - dev:  SQLite数据库，热重载，详细日志，开发工具启用
  - test: SQLite数据库，生产模式，中等日志，测试配置
  - prod: PostgreSQL数据库，生产优化，错误日志，安全配置
EOF
}

# 验证项目环境
validate_project_environment() {
    local env=$1
    
    log_info "验证后端项目环境..."
    
    # 检查项目目录
    if [[ ! -f "$SERVICE_DIR/pyproject.toml" ]]; then
        log_error "pyproject.toml 不存在: $SERVICE_DIR/pyproject.toml"
        return 1
    fi
    
    # 检查配置目录
    if [[ ! -d "$CONFIG_DIR" ]]; then
        log_error "配置目录不存在: $CONFIG_DIR"
        return 1
    fi
    
    # 检查环境配置文件
    local env_file="$CONFIG_DIR/environments/${env}.env"
    if [[ ! -f "$env_file" ]]; then
        log_error "环境配置文件不存在: $env_file"
        return 1
    fi
    
    # 检查Compose配置文件
    local compose_file="$CONFIG_DIR/compose/docker-compose.${env}.yml"
    if [[ ! -f "$compose_file" ]]; then
        log_error "Compose配置文件不存在: $compose_file"
        return 1
    fi
    
    # 检查Dockerfile
    local dockerfile="$CONFIG_DIR/docker/Dockerfile.${env}"
    if [[ ! -f "$dockerfile" ]]; then
        log_error "Dockerfile不存在: $dockerfile"
        return 1
    fi
    
    log_success "项目环境验证完成"
}

# 获取配置文件路径
get_config_paths() {
    local env=$1
    ENV_FILE="$CONFIG_DIR/environments/${env}.env"
    COMPOSE_FILE="$CONFIG_DIR/compose/docker-compose.${env}.yml"
    DOCKERFILE="$CONFIG_DIR/docker/Dockerfile.${env}"
}

# 构建镜像
build_images() {
    local env=$1
    local build_args=""

    if [[ "$NO_CACHE" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi

    if [[ "$PULL_LATEST" == "true" ]]; then
        build_args="$build_args --pull"
    fi

    cd "$SERVICE_DIR"

    # 添加构建参数支持镜像源
    local build_args_extra=""
    if [[ -f "$PROJECT_ROOT/config/mirrors.env" ]]; then
        source "$PROJECT_ROOT/config/mirrors.env"
        build_args_extra="--build-arg PIP_INDEX_URL=${PIP_INDEX_URL:-https://pypi.tuna.tsinghua.edu.cn/simple} --build-arg PIP_TRUSTED_HOST=${PIP_TRUSTED_HOST:-pypi.tuna.tsinghua.edu.cn}"
    fi

    local build_cmd="$COMPOSE_CMD -f '$COMPOSE_FILE' --env-file '$ENV_FILE' build $build_args $build_args_extra"
    execute_with_progress "构建 $env 环境镜像" "$build_cmd" "always"
}

# 部署服务
deploy_services() {
    local env=$1

    cd "$SERVICE_DIR"

    # 确保网络存在
    local network_name
    network_name=$(grep "^NETWORK_NAME=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"' | tr -d "'")
    network_name=${network_name:-"zentao-${env}-network"}
    ensure_network "$network_name"

    # 启动服务
    local deploy_cmd="$COMPOSE_CMD -f '$COMPOSE_FILE' --env-file '$ENV_FILE' up -d"
    execute_with_progress "部署 $env 环境服务" "$deploy_cmd" "always"

    # 等待服务就绪
    wait_for_service "http://localhost:8000/health"
}

# 启动服务
start_services() {
    local env=$1

    log_info "启动 $env 环境服务..."

    cd "$SERVICE_DIR"
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" start

    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    local env=$1

    log_info "停止 $env 环境服务..."

    cd "$SERVICE_DIR"
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" stop

    log_success "服务停止完成"
}

# 重启服务
restart_services() {
    local env=$1

    log_info "重启 $env 环境服务..."

    cd "$SERVICE_DIR"
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" restart

    log_success "服务重启完成"
}

# 查看服务状态
show_status() {
    local env=$1

    log_info "查看 $env 环境服务状态..."

    cd "$SERVICE_DIR"
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
}

# 查看日志
show_logs() {
    local env=$1

    log_info "查看 $env 环境日志..."

    cd "$SERVICE_DIR"
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f
}

# 健康检查
health_check() {
    local env=$1
    health_check "http://localhost:8000/health" "后端服务"
}

# 清理环境
clean_environment() {
    local env=$1

    log_warning "清理 $env 环境（这将删除所有数据）..."

    # 确认操作
    if [[ "$FORCE_REBUILD" != "true" ]]; then
        echo -n "确认清理 $env 环境？这将删除所有容器、镜像和数据卷 [y/N]: "
        read -r confirm
        if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
            log_info "取消清理操作"
            return 0
        fi
    fi

    cd "$SERVICE_DIR"
    log_info "停止并删除服务..."
    $COMPOSE_CMD -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down --volumes --remove-orphans || true

    # 删除镜像
    local image_name="zentao-mcp-backend-${env}"
    if [[ "$CONTAINER_ENGINE" == "docker" ]]; then
        docker rmi "$image_name" 2>/dev/null || true
    else
        podman rmi "$image_name" 2>/dev/null || true
    fi

    log_success "环境清理完成"
}

# 初始化数据库和管理员
init_database() {
    local env=$1
    
    log_info "初始化 $env 环境数据库..."
    
    cd "$SERVICE_DIR"
    
    # 检查服务是否运行
    if ! curl -f -s http://localhost:8000/health &> /dev/null; then
        log_error "后端服务未运行，请先启动服务"
        return 1
    fi
    
    # 运行初始化脚本
    if [[ -f "init_system.py" ]]; then
        log_info "执行系统初始化..."
        uv run python init_system.py
    fi
    
    if [[ -f "init_admin.py" ]]; then
        log_info "执行管理员初始化..."
        uv run python init_admin.py
    fi
    
    log_success "数据库初始化完成"
}

# 主函数
main() {
    log_info "Zentao MCP Backend Service 部署脚本启动"

    # 初始化公共环境
    init_common_environment

    # 解析命令行参数
    local remaining_args=()
    while [[ $# -gt 0 ]]; do
        local result
        result=$(parse_common_args "$@")
        local parse_result=$?
        
        if [[ $parse_result -eq 2 ]]; then
            show_help
            exit 0
        elif [[ -n "$result" ]]; then
            remaining_args+=("$result")
            shift
        else
            break
        fi
    done

    # 处理剩余参数
    if [[ ${#remaining_args[@]} -gt 0 ]]; then
        for arg in "${remaining_args[@]}"; do
            case $arg in
                init)
                    ACTION="init"
                    ;;
                --quiet)
                    SHOW_PROGRESS=false
                    ;;
                *)
                    log_error "未知参数: $arg"
                    show_help
                    exit 1
                    ;;
            esac
        done
    fi

    # 验证环境参数
    validate_environment "$ENVIRONMENT" || exit 1

    # 检查Python环境
    check_python_environment || exit 1

    # 检测和设置容器引擎
    detect_container_engine || exit 1
    setup_compose_command || exit 1

    # 验证项目环境
    validate_project_environment "$ENVIRONMENT" || exit 1

    # 获取配置文件路径
    get_config_paths "$ENVIRONMENT"

    log_info "使用配置:"
    log_info "  环境: $ENVIRONMENT"
    log_info "  容器引擎: $CONTAINER_ENGINE"
    log_info "  动作: $ACTION"
    log_info "  配置文件: $ENV_FILE"
    log_info "  Compose文件: $COMPOSE_FILE"

    # 执行对应的动作
    case $ACTION in
        deploy)
            build_images "$ENVIRONMENT"
            deploy_services "$ENVIRONMENT"
            show_status "$ENVIRONMENT"
            ;;
        build)
            build_images "$ENVIRONMENT"
            ;;
        start)
            start_services "$ENVIRONMENT"
            ;;
        stop)
            stop_services "$ENVIRONMENT"
            ;;
        restart)
            restart_services "$ENVIRONMENT"
            ;;
        status)
            show_status "$ENVIRONMENT"
            ;;
        logs)
            show_logs "$ENVIRONMENT"
            ;;
        health)
            health_check "$ENVIRONMENT"
            ;;
        clean)
            clean_environment "$ENVIRONMENT"
            ;;
        init)
            init_database "$ENVIRONMENT"
            ;;
        *)
            log_error "未知动作: $ACTION"
            show_help
            exit 1
            ;;
    esac

    log_success "操作完成!"
}

# 执行主函数
main "$@"
