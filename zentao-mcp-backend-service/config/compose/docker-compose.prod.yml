version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: zentao-postgres-prod
    environment:
      POSTGRES_DB: zentao_mcp_prod
      POSTGRES_USER: zentao_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-change_me}
    volumes:
      - zentao-postgres-prod-data:/var/lib/postgresql/data
    networks:
      - zentao-prod
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U zentao_user -d zentao_mcp_prod"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ../../../zentao-mcp-backend-service
      dockerfile: config/docker/Dockerfile.prod
    container_name: ${CONTAINER_NAME:-zentao-backend-prod}
    env_file:
      - ../environments/prod.env
    ports:
      - "8000:8000"
    volumes:
      - zentao-backend-prod-logs:/app/logs
    networks:
      - zentao-prod
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      replicas: ${REPLICAS:-2}
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  zentao-prod:
    driver: bridge
    name: ${NETWORK_NAME:-zentao-prod-network}

volumes:
  zentao-postgres-prod-data:
    driver: local
  zentao-backend-prod-logs:
    driver: local
