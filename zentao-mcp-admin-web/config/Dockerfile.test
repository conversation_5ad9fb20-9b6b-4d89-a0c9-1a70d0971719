# 测试环境Dockerfile - 多阶段构建
# 使用ARG支持包管理器镜像源配置
ARG NPM_REGISTRY=https://registry.npmmirror.com
ARG APK_MIRROR=mirrors.aliyun.com

FROM node:18-alpine AS builder

WORKDIR /app

# 配置时区和语言环境
ENV TZ=Asia/Shanghai

# 配置Alpine镜像源
RUN sed -i "s/dl-cdn.alpinelinux.org/${APK_MIRROR}/g" /etc/apk/repositories

# 配置npm镜像源
RUN npm config set registry ${NPM_REGISTRY}

# 复制package文件
COPY package*.json ./
COPY bun.lockb* ./

# 安装依赖
RUN if [ -f "bun.lockb" ]; then \
        npm install -g bun && bun install; \
    else \
        npm ci; \
    fi

# 复制源代码
COPY . .

# 构建应用
RUN if [ -f "bun.lockb" ]; then \
        bun run build; \
    else \
        npm run build; \
    fi

# 生产阶段
FROM nginx:alpine

# 配置Alpine镜像源
RUN sed -i "s/dl-cdn.alpinelinux.org/${APK_MIRROR}/g" /etc/apk/repositories

# 安装curl用于健康检查
RUN apk add --no-cache curl tzdata

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY config/nginx.conf /etc/nginx/nginx.conf

# 创建非root用户
RUN addgroup -g 1001 -S nginx && \
    adduser -S nginx -u 1001

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    touch /var/run/nginx.pid && \
    chown -R nginx:nginx /var/run/nginx.pid

# 切换到非root用户
USER nginx

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# 启动命令
CMD ["nginx", "-g", "daemon off;"]
