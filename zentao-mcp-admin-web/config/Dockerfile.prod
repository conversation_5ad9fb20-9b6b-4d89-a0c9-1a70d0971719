# 生产环境Dockerfile - 多阶段构建优化
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./
COPY bun.lockb* ./

# 安装依赖
RUN if [ -f "bun.lockb" ]; then \
        npm install -g bun && bun install --production; \
    else \
        npm ci --only=production; \
    fi

# 复制源代码
COPY . .

# 构建应用（生产优化）
ENV NODE_ENV=production
RUN if [ -f "bun.lockb" ]; then \
        bun run build; \
    else \
        npm run build; \
    fi

# 生产阶段
FROM nginx:alpine

# 安装curl用于健康检查
RUN apk add --no-cache curl

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY config/nginx.conf /etc/nginx/nginx.conf

# 复制SSL证书（如果存在）
COPY config/ssl/* /etc/nginx/ssl/ 2>/dev/null || true

# 创建非root用户
RUN addgroup -g 1001 -S nginx && \
    adduser -S nginx -u 1001

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    touch /var/run/nginx.pid && \
    chown -R nginx:nginx /var/run/nginx.pid

# 设置SSL目录权限（如果存在）
RUN if [ -d "/etc/nginx/ssl" ]; then \
        chown -R nginx:nginx /etc/nginx/ssl; \
    fi

# 切换到非root用户
USER nginx

# 暴露端口
EXPOSE 8080 8443

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# 启动命令
CMD ["nginx", "-g", "daemon off;"]
