#!/bin/bash
# ============================================================================
# Zentao MCP Admin Web 部署脚本
# 支持 dev/test/prod 三种环境的独立部署
# ============================================================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SERVICE_DIR="$SCRIPT_DIR"
CONFIG_DIR="$SERVICE_DIR/config"

# 加载公共函数库
if [[ -f "$PROJECT_ROOT/scripts/common.sh" ]]; then
    source "$PROJECT_ROOT/scripts/common.sh"
else
    echo "错误: 找不到公共函数库 $PROJECT_ROOT/scripts/common.sh"
    exit 1
fi

# 显示帮助信息
show_help() {
    cat << 'EOF'
Zentao MCP Admin Web 部署脚本

用法: ./deploy.sh [选项] <环境> [动作]

环境:
  dev       开发环境 (默认) - Vite开发服务器，热重载
  test      测试环境 - 静态构建 + Nginx容器
  prod      生产环境 - 优化构建 + Nginx + HTTPS

动作:
  deploy    构建并部署服务 (默认)
  build     仅构建项目
  start     启动已存在的服务
  stop      停止服务
  restart   重启服务
  logs      查看服务日志
  status    查看服务状态
  clean     清理环境（停止并删除容器、镜像、卷）
  test      运行项目测试

选项:
  -e, --engine ENGINE    指定容器引擎 (docker|podman)
  -f, --force           强制重新构建
  -v, --verbose         详细输出模式
  --no-cache            构建时不使用缓存
  --pull                构建前拉取最新基础镜像
  -h, --help            显示帮助信息

示例:
  ./deploy.sh dev                    # 开发环境部署
  ./deploy.sh prod --engine=podman   # 使用Podman部署生产环境
  ./deploy.sh test build --force     # 强制重新构建测试环境
  ./deploy.sh dev logs               # 查看开发环境日志
  ./deploy.sh prod clean             # 清理生产环境

环境说明:
  - dev:  Vite开发服务器，热重载，开发工具启用
  - test: 静态构建，Nginx容器，测试配置
  - prod: 优化构建，Nginx + HTTPS，生产配置
EOF
}

# 验证项目环境
validate_project_environment() {
    local env=$1
    
    log_info "验证前端项目环境..."
    
    # 检查项目目录
    if [[ ! -f "$SERVICE_DIR/package.json" ]]; then
        log_error "package.json 不存在: $SERVICE_DIR/package.json"
        return 1
    fi
    
    # 检查配置目录
    if [[ ! -d "$CONFIG_DIR" ]]; then
        log_info "创建配置目录: $CONFIG_DIR"
        mkdir -p "$CONFIG_DIR"
    fi
    
    # 检查环境配置文件
    local env_file="$CONFIG_DIR/.env.${env}"
    if [[ ! -f "$env_file" ]]; then
        log_warning "环境配置文件不存在: $env_file"
        log_info "创建默认配置文件..."
        create_default_env_file "$env"
    fi
    
    # 检查Dockerfile和compose文件
    local dockerfile="$CONFIG_DIR/Dockerfile.${env}"
    if [[ ! -f "$dockerfile" ]]; then
        log_warning "Dockerfile不存在: $dockerfile"
        log_info "创建默认Dockerfile..."
        create_default_dockerfile "$env"
    fi

    local compose_file="$CONFIG_DIR/docker-compose.${env}.yml"
    if [[ ! -f "$compose_file" ]]; then
        log_warning "Compose配置文件不存在: $compose_file"
        log_info "创建默认Compose文件..."
        create_default_compose_file "$env"
    fi
    
    log_success "项目环境验证完成"
}

# 创建默认环境配置文件
create_default_env_file() {
    local env=$1
    local env_file="$CONFIG_DIR/.env.${env}"
    
    case $env in
        dev)
            cat > "$env_file" << 'EOF'
# 开发环境配置
VITE_API_BASE_URL=http://localhost:8000
VITE_API_VERSION=v1
VITE_API_TIMEOUT=10000

# 开发配置
VITE_APP_ENV=development
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true

# 调试配置
VITE_LOG_LEVEL=debug
VITE_ENABLE_CONSOLE=true

# 开发服务器配置
VITE_HOST=0.0.0.0
VITE_PORT=3000
VITE_OPEN=false
EOF
            ;;
        test)
            cat > "$env_file" << 'EOF'
# 测试环境配置
VITE_API_BASE_URL=http://localhost:8000
VITE_API_VERSION=v1
VITE_API_TIMEOUT=15000

# 测试配置
VITE_APP_ENV=testing
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false

# 调试配置
VITE_LOG_LEVEL=info
VITE_ENABLE_CONSOLE=false

# 构建配置
VITE_BUILD_SOURCEMAP=true
VITE_BUILD_MINIFY=true
EOF
            ;;
        prod)
            cat > "$env_file" << 'EOF'
# 生产环境配置
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_API_VERSION=v1
VITE_API_TIMEOUT=20000

# 生产配置
VITE_APP_ENV=production
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false

# 性能配置
VITE_LOG_LEVEL=error
VITE_ENABLE_CONSOLE=false
VITE_ENABLE_PWA=true

# 构建配置
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_MINIFY=true
VITE_BUILD_GZIP=true
EOF
            ;;
    esac
    
    log_success "已创建默认环境配置: $env_file"
}

# 创建默认Dockerfile
create_default_dockerfile() {
    local env=$1
    local dockerfile="$CONFIG_DIR/Dockerfile.${env}"
    
    cat > "$dockerfile" << 'EOF'
# 多阶段构建 - 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./
COPY bun.lockb* ./

# 安装依赖
RUN if [ -f "bun.lockb" ]; then \
        npm install -g bun && bun install; \
    else \
        npm ci --only=production; \
    fi

# 复制源代码
COPY . .

# 构建应用
RUN if [ -f "bun.lockb" ]; then \
        bun run build; \
    else \
        npm run build; \
    fi

# 生产阶段
FROM nginx:alpine

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY config/nginx.conf /etc/nginx/nginx.conf

# 创建非root用户
RUN addgroup -g 1001 -S nginx && \
    adduser -S nginx -u 1001

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d

# 切换到非root用户
USER nginx

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# 启动命令
CMD ["nginx", "-g", "daemon off;"]
EOF
    
    log_success "已创建默认Dockerfile: $dockerfile"
}

# 创建默认Compose文件
create_default_compose_file() {
    local env=$1
    local compose_file="$CONFIG_DIR/docker-compose.${env}.yml"

    case $env in
        test)
            cat > "$compose_file" << 'EOF'
version: '3.8'

services:
  frontend:
    build:
      context: ../
      dockerfile: config/Dockerfile.test
    container_name: zentao-frontend-test
    ports:
      - "3000:8080"
    environment:
      - NODE_ENV=test
    networks:
      - zentao-test
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  zentao-test:
    driver: bridge
    name: zentao-test-network
EOF
            ;;
        prod)
            cat > "$compose_file" << 'EOF'
version: '3.8'

services:
  frontend:
    build:
      context: ../
      dockerfile: config/Dockerfile.prod
    container_name: zentao-frontend-prod
    ports:
      - "80:8080"
      - "443:8443"
    environment:
      - NODE_ENV=production
    networks:
      - zentao-prod
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  zentao-prod:
    driver: bridge
    name: zentao-prod-network
EOF
            ;;
    esac

    log_success "已创建默认Compose配置: $compose_file"
}

# 获取配置文件路径
get_config_paths() {
    local env=$1
    ENV_FILE="$CONFIG_DIR/.env.${env}"
    COMPOSE_FILE="$CONFIG_DIR/docker-compose.${env}.yml"
    DOCKERFILE="$CONFIG_DIR/Dockerfile.${env}"
}

# 安装依赖
install_dependencies() {
    cd "$SERVICE_DIR"

    local install_cmd=""
    case $PACKAGE_MANAGER in
        bun)
            install_cmd="bun install"
            ;;
        pnpm)
            install_cmd="pnpm install"
            ;;
        yarn)
            install_cmd="yarn install"
            ;;
        npm)
            install_cmd="npm install"
            ;;
    esac

    execute_with_progress "安装前端依赖 ($PACKAGE_MANAGER)" "$install_cmd" "always"
}

# 运行测试
run_tests() {
    local env=$1

    log_info "运行 $env 环境测试..."

    cd "$SERVICE_DIR"

    # 类型检查
    if [[ -f "tsconfig.json" ]]; then
        log_info "执行TypeScript类型检查..."
        case $PACKAGE_MANAGER in
            bun)
                bun run type-check || log_warning "类型检查有警告"
                ;;
            *)
                $PACKAGE_MANAGER run type-check || log_warning "类型检查有警告"
                ;;
        esac
    fi

    # ESLint检查
    log_info "执行ESLint代码检查..."
    case $PACKAGE_MANAGER in
        bun)
            bun run lint || log_warning "代码检查有警告"
            ;;
        *)
            $PACKAGE_MANAGER run lint || log_warning "代码检查有警告"
            ;;
    esac

    log_success "测试完成"
}

# 构建项目
build_project() {
    local env=$1

    cd "$SERVICE_DIR"

    # 复制环境配置文件
    if [[ -f "$ENV_FILE" ]]; then
        cp "$ENV_FILE" ".env.local"
        log_debug "已复制环境配置文件"
    fi

    # 执行构建
    local build_cmd=""
    case $PACKAGE_MANAGER in
        bun)
            build_cmd="bun run build"
            ;;
        *)
            build_cmd="$PACKAGE_MANAGER run build"
            ;;
    esac

    execute_with_progress "构建 $env 环境项目 ($PACKAGE_MANAGER)" "$build_cmd" "always"

    # 检查构建结果
    if [[ -d "dist" ]]; then
        log_info "构建文件大小:"
        du -sh dist/
    else
        log_error "构建失败，dist目录不存在"
        return 1
    fi
}

# 开发环境启动
start_dev_server() {
    local env=$1

    log_info "启动 $env 环境开发服务器..."

    cd "$SERVICE_DIR"

    # 复制环境配置文件
    if [[ -f "$ENV_FILE" ]]; then
        cp "$ENV_FILE" ".env.local"
        log_debug "已复制环境配置文件"
    fi

    # 启动开发服务器
    case $PACKAGE_MANAGER in
        bun)
            bun run dev
            ;;
        *)
            $PACKAGE_MANAGER run dev
            ;;
    esac
}

# 容器化部署
deploy_with_container() {
    local env=$1

    cd "$SERVICE_DIR"

    # 创建网络（Podman需要）
    local network_name="zentao-${env}-network"
    ensure_network "$network_name"

    # 构建和启动服务
    local build_args=""
    if [[ "$NO_CACHE" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi

    if [[ "$PULL_LATEST" == "true" ]]; then
        build_args="$build_args --pull"
    fi

    # 添加构建参数支持镜像源
    local build_args_extra=""
    if [[ -f "$PROJECT_ROOT/config/mirrors.env" ]]; then
        source "$PROJECT_ROOT/config/mirrors.env"
        build_args_extra="--build-arg NPM_REGISTRY=${NPM_REGISTRY:-https://registry.npmmirror.com} --build-arg APK_MIRROR=${APK_MIRROR:-mirrors.aliyun.com}"
    fi

    local build_cmd="$COMPOSE_CMD -f '$COMPOSE_FILE' build $build_args $build_args_extra"
    execute_with_progress "构建 $env 环境容器镜像" "$build_cmd" "always"

    local deploy_cmd="$COMPOSE_CMD -f '$COMPOSE_FILE' up -d"
    execute_with_progress "启动 $env 环境容器服务" "$deploy_cmd" "always"
}

# 启动服务
start_services() {
    local env=$1

    if [[ "$env" == "dev" ]]; then
        start_dev_server "$env"
    else
        log_info "启动 $env 环境容器服务..."
        cd "$SERVICE_DIR"
        $COMPOSE_CMD -f "$COMPOSE_FILE" start
        log_success "服务启动完成"
    fi
}

# 停止服务
stop_services() {
    local env=$1

    if [[ "$env" == "dev" ]]; then
        log_info "开发环境请使用 Ctrl+C 停止开发服务器"
    else
        log_info "停止 $env 环境服务..."
        cd "$SERVICE_DIR"
        $COMPOSE_CMD -f "$COMPOSE_FILE" stop
        log_success "服务停止完成"
    fi
}

# 重启服务
restart_services() {
    local env=$1

    if [[ "$env" == "dev" ]]; then
        log_info "开发环境请重新运行部署命令"
    else
        log_info "重启 $env 环境服务..."
        cd "$SERVICE_DIR"
        $COMPOSE_CMD -f "$COMPOSE_FILE" restart
        log_success "服务重启完成"
    fi
}

# 查看服务状态
show_status() {
    local env=$1

    if [[ "$env" == "dev" ]]; then
        log_info "开发环境状态检查..."
        if curl -f -s http://localhost:3000 &> /dev/null; then
            log_success "开发服务器运行中 (http://localhost:3000)"
        else
            log_warning "开发服务器未运行"
        fi
    else
        log_info "查看 $env 环境服务状态..."
        cd "$SERVICE_DIR"
        $COMPOSE_CMD -f "$COMPOSE_FILE" ps
    fi
}

# 查看日志
show_logs() {
    local env=$1

    if [[ "$env" == "dev" ]]; then
        log_info "开发环境日志请查看终端输出"
    else
        log_info "查看 $env 环境日志..."
        cd "$SERVICE_DIR"
        $COMPOSE_CMD -f "$COMPOSE_FILE" logs -f
    fi
}

# 清理环境
clean_environment() {
    local env=$1

    if [[ "$env" == "dev" ]]; then
        log_info "清理开发环境..."
        cd "$SERVICE_DIR"
        rm -rf node_modules/.cache dist .env.local
        log_success "开发环境清理完成"
    else
        log_warning "清理 $env 环境（这将删除所有容器和镜像）..."

        # 确认操作
        if [[ "$FORCE_REBUILD" != "true" ]]; then
            echo -n "确认清理 $env 环境？这将删除所有容器和镜像 [y/N]: "
            read -r confirm
            if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
                log_info "取消清理操作"
                return 0
            fi
        fi

        cd "$SERVICE_DIR"
        $COMPOSE_CMD -f "$COMPOSE_FILE" down --volumes --remove-orphans || true

        # 删除镜像
        local image_name="zentao-frontend-${env}"
        if [[ "$CONTAINER_ENGINE" == "docker" ]]; then
            docker rmi "$image_name" 2>/dev/null || true
        else
            podman rmi "$image_name" 2>/dev/null || true
        fi

        log_success "环境清理完成"
    fi
}

# 主函数
main() {
    log_info "Zentao MCP Admin Web 部署脚本启动"

    # 初始化公共环境
    init_common_environment

    # 解析命令行参数
    local remaining_args=()
    while [[ $# -gt 0 ]]; do
        local result
        result=$(parse_common_args "$@")
        local parse_result=$?
        
        if [[ $parse_result -eq 2 ]]; then
            show_help
            exit 0
        elif [[ -n "$result" ]]; then
            remaining_args+=("$result")
            shift
        else
            break
        fi
    done

    # 验证环境参数
    validate_environment "$ENVIRONMENT" || exit 1

    # 检查Node.js环境
    check_node_environment || exit 1

    # 对于非开发环境，检测容器引擎
    if [[ "$ENVIRONMENT" != "dev" ]] || [[ "$ACTION" =~ ^(deploy|build|start|stop|restart|logs|status|clean)$ ]]; then
        detect_container_engine || exit 1
        setup_compose_command || exit 1
    fi

    # 验证项目环境
    validate_project_environment "$ENVIRONMENT" || exit 1

    # 获取配置文件路径
    get_config_paths "$ENVIRONMENT"

    log_info "使用配置:"
    log_info "  环境: $ENVIRONMENT"
    log_info "  动作: $ACTION"
    log_info "  包管理器: $PACKAGE_MANAGER"
    if [[ "$ENVIRONMENT" != "dev" ]]; then
        log_info "  容器引擎: $CONTAINER_ENGINE"
        log_info "  配置文件: $ENV_FILE"
        log_info "  Compose文件: $COMPOSE_FILE"
    fi

    # 执行对应的动作
    case $ACTION in
        deploy)
            install_dependencies
            if [[ "$ENVIRONMENT" == "dev" ]]; then
                log_info "开发环境部署 - 启动开发服务器"
                start_dev_server "$ENVIRONMENT"
            else
                build_project "$ENVIRONMENT"
                deploy_with_container "$ENVIRONMENT"
            fi
            ;;
        build)
            install_dependencies
            if [[ "$ENVIRONMENT" == "dev" ]]; then
                log_info "开发环境构建 - 仅安装依赖"
            else
                build_project "$ENVIRONMENT"
            fi
            ;;
        start)
            start_services "$ENVIRONMENT"
            ;;
        stop)
            stop_services "$ENVIRONMENT"
            ;;
        restart)
            restart_services "$ENVIRONMENT"
            ;;
        status)
            show_status "$ENVIRONMENT"
            ;;
        logs)
            show_logs "$ENVIRONMENT"
            ;;
        clean)
            clean_environment "$ENVIRONMENT"
            ;;
        test)
            install_dependencies
            run_tests "$ENVIRONMENT"
            ;;
        *)
            log_error "动作 $ACTION 暂未实现"
            show_help
            exit 1
            ;;
    esac

    log_success "操作完成!"
}

# 执行主函数
main "$@"
