#!/bin/bash
# ============================================================================
# Zentao MCP 统一部署脚本
# 支持单项目和全项目部署，调用各子项目的部署脚本
# ============================================================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"

# 加载公共函数库
if [[ -f "$PROJECT_ROOT/scripts/common.sh" ]]; then
    source "$PROJECT_ROOT/scripts/common.sh"
else
    echo "错误: 找不到公共函数库 $PROJECT_ROOT/scripts/common.sh"
    exit 1
fi

# 显示帮助信息
show_help() {
    cat << 'EOF'
Zentao MCP 统一部署脚本

用法: ./deploy-zentao.sh [选项] <项目> <环境> [动作]

项目:
  backend     后端服务 (zentao-mcp-backend-service)
  frontend    前端服务 (zentao-mcp-admin-web)
  client      客户端服务 (zentao-mcp-client)
  all         所有项目

环境:
  dev         开发环境 (默认)
  test        测试环境
  prod        生产环境

动作:
  deploy      构建并部署服务 (默认)
  build       仅构建项目
  start       启动已存在的服务
  stop        停止服务
  restart     重启服务
  logs        查看服务日志
  status      查看服务状态
  clean       清理环境
  health      检查服务健康状态
  test        运行项目测试

选项:
  -e, --engine ENGINE    指定容器引擎 (docker|podman)
  -f, --force           强制重新构建
  -v, --verbose         详细输出模式
  --no-cache            构建时不使用缓存
  --pull                构建前拉取最新基础镜像
  --quiet               静默模式，不显示进度指示器
  -h, --help            显示帮助信息

示例:
  ./deploy-zentao.sh backend dev deploy          # 部署后端开发环境
  ./deploy-zentao.sh frontend prod build         # 构建前端生产环境
  ./deploy-zentao.sh client test start           # 启动客户端测试环境
  ./deploy-zentao.sh all dev deploy              # 部署所有项目开发环境
  ./deploy-zentao.sh backend prod --force        # 强制重新部署后端生产环境

项目说明:
  - backend:  FastAPI后端服务，提供API接口和数据管理
  - frontend: Vue.js前端界面，提供Web管理控制台
  - client:   轻量级客户端，支持HTTP/STDIO/SSE三种模式
  - all:      按顺序部署所有项目 (backend → frontend → client)

环境说明:
  - dev:  开发环境，本地调试，详细日志
  - test: 测试环境，自动化测试，中等日志
  - prod: 生产环境，性能优化，错误日志
EOF
}

# 部署所有项目
deploy_all_projects() {
    local env=$1
    local action=$2
    shift 2
    local extra_args=("$@")
    
    log_info "开始部署所有项目 ($env 环境, $action 动作)"
    
    local projects=("backend" "frontend" "client")
    local failed_projects=()
    
    for project in "${projects[@]}"; do
        log_info "========== 部署 $project 项目 =========="
        
        if ! call_project_deploy "$project" "$env" "$action" "${extra_args[@]}"; then
            failed_projects+=("$project")
            log_error "$project 项目部署失败"
            
            # 询问是否继续
            if [[ "$FORCE_REBUILD" != "true" ]]; then
                echo -n "是否继续部署其他项目？ [y/N]: "
                read -r confirm
                if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
                    log_info "用户取消部署"
                    break
                fi
            fi
        else
            log_success "$project 项目部署成功"
        fi
        
        echo ""
    done
    
    # 部署结果汇总
    log_info "========== 部署结果汇总 =========="
    
    if [[ ${#failed_projects[@]} -eq 0 ]]; then
        log_success "所有项目部署成功！"
        return 0
    else
        log_error "以下项目部署失败: ${failed_projects[*]}"
        return 1
    fi
}

# 显示所有项目状态
show_all_status() {
    local env=$1
    
    log_info "========== 所有项目状态 ($env 环境) =========="
    
    local projects=("backend" "frontend" "client")
    
    for project in "${projects[@]}"; do
        log_info "---------- $project 项目状态 ----------"
        call_project_deploy "$project" "$env" "status" || true
        echo ""
    done
}

# 停止所有项目
stop_all_projects() {
    local env=$1
    
    log_info "停止所有项目 ($env 环境)"
    
    # 按相反顺序停止
    local projects=("client" "frontend" "backend")
    
    for project in "${projects[@]}"; do
        log_info "停止 $project 项目..."
        call_project_deploy "$project" "$env" "stop" || true
    done
    
    log_success "所有项目已停止"
}

# 清理所有项目
clean_all_projects() {
    local env=$1
    
    log_warning "清理所有项目 ($env 环境)"
    
    # 确认操作
    if [[ "$FORCE_REBUILD" != "true" ]]; then
        echo -n "确认清理所有项目？这将删除所有容器、镜像和数据 [y/N]: "
        read -r confirm
        if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
            log_info "取消清理操作"
            return 0
        fi
    fi
    
    # 按相反顺序清理
    local projects=("client" "frontend" "backend")
    
    for project in "${projects[@]}"; do
        log_info "清理 $project 项目..."
        call_project_deploy "$project" "$env" "clean" --force || true
    done
    
    log_success "所有项目已清理"
}

# 健康检查所有项目
health_check_all() {
    local env=$1
    
    log_info "========== 健康检查所有项目 ($env 环境) =========="
    
    local projects=("backend" "frontend" "client")
    local unhealthy_projects=()
    
    for project in "${projects[@]}"; do
        log_info "检查 $project 项目健康状态..."
        if ! call_project_deploy "$project" "$env" "health" 2>/dev/null; then
            unhealthy_projects+=("$project")
        fi
    done
    
    if [[ ${#unhealthy_projects[@]} -eq 0 ]]; then
        log_success "所有项目健康检查通过！"
        return 0
    else
        log_error "以下项目健康检查失败: ${unhealthy_projects[*]}"
        return 1
    fi
}

# 主函数
main() {
    log_info "Zentao MCP 统一部署脚本启动"

    # 初始化公共环境
    init_common_environment

    # 解析命令行参数
    local PROJECT=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -e|--engine)
                CONTAINER_ENGINE="$2"
                shift 2
                ;;
            --engine=*)
                CONTAINER_ENGINE="${1#*=}"
                shift
                ;;
            -f|--force)
                FORCE_REBUILD=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --no-cache)
                NO_CACHE=true
                shift
                ;;
            --pull)
                PULL_LATEST=true
                shift
                ;;
            --quiet)
                SHOW_PROGRESS=false
                shift
                ;;
            backend|frontend|client|all)
                PROJECT="$1"
                shift
                ;;
            dev|test|prod)
                ENVIRONMENT="$1"
                shift
                ;;
            deploy|build|start|stop|restart|logs|status|clean|health|test)
                ACTION="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 验证项目参数
    if [[ -z "$PROJECT" ]]; then
        log_error "请指定项目: backend, frontend, client, all"
        show_help
        exit 1
    fi

    # 验证环境参数
    validate_environment "$ENVIRONMENT" || exit 1

    log_info "使用配置:"
    log_info "  项目: $PROJECT"
    log_info "  环境: $ENVIRONMENT"
    log_info "  动作: $ACTION"
    if [[ -n "$CONTAINER_ENGINE" ]]; then
        log_info "  容器引擎: $CONTAINER_ENGINE"
    fi

    # 执行对应的动作
    case $PROJECT in
        all)
            case $ACTION in
                deploy|build|start|restart|test)
                    deploy_all_projects "$ENVIRONMENT" "$ACTION"
                    ;;
                stop)
                    stop_all_projects "$ENVIRONMENT"
                    ;;
                status)
                    show_all_status "$ENVIRONMENT"
                    ;;
                clean)
                    clean_all_projects "$ENVIRONMENT"
                    ;;
                health)
                    health_check_all "$ENVIRONMENT"
                    ;;
                logs)
                    log_error "logs 动作不支持 all 项目，请指定具体项目"
                    exit 1
                    ;;
                *)
                    log_error "未知动作: $ACTION"
                    show_help
                    exit 1
                    ;;
            esac
            ;;
        backend|frontend|client)
            call_project_deploy "$PROJECT" "$ENVIRONMENT" "$ACTION"
            ;;
        *)
            log_error "未知项目: $PROJECT"
            show_help
            exit 1
            ;;
    esac

    log_success "操作完成!"
}

# 执行主函数
main "$@"
