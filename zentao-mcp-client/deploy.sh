#!/bin/bash
# ============================================================================
# Zentao MCP Client 部署脚本
# 支持 dev/test/prod 三种环境的独立部署
# ============================================================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
SERVICE_DIR="$SCRIPT_DIR"

# 默认参数
SERVICE_MODE="stdio"
HOST="localhost"
PORT=8080
DEPLOYMENT_TYPE=""

# 加载公共函数库
if [[ -f "$PROJECT_ROOT/scripts/common.sh" ]]; then
    source "$PROJECT_ROOT/scripts/common.sh"
else
    echo "错误: 找不到公共函数库 $PROJECT_ROOT/scripts/common.sh"
    exit 1
fi

# 显示帮助信息
show_help() {
    cat << 'EOF'
Zentao MCP Client 部署脚本

用法: ./deploy.sh [选项] <环境> [动作]

环境:
  dev       开发环境 (默认) - 源码运行，项目配置
  test      测试环境 - pip安装，环境变量配置
  prod      生产环境 - 可执行文件，系统服务

动作:
  deploy    部署客户端服务 (默认)
  build     构建可执行文件 (仅prod环境)
  start     启动服务
  stop      停止服务
  restart   重启服务
  status    查看服务状态
  config    配置向导
  test      运行连接测试
  clean     清理环境
  install-service  安装系统服务 (仅prod环境)

选项:
  -t, --type TYPE       部署类型 (source|pip|executable)
  -m, --mode MODE       服务模式 (stdio|http|sse)
  --host HOST           HTTP模式监听地址 (默认: localhost)
  --port PORT           HTTP/SSE模式端口 (默认: 8080)
  -f, --force           强制重新构建
  -v, --verbose         详细输出模式
  -h, --help            显示帮助信息

示例:
  ./deploy.sh dev deploy                    # 开发环境部署
  ./deploy.sh test deploy --type=pip        # 测试环境pip安装
  ./deploy.sh prod build                    # 生产环境构建可执行文件
  ./deploy.sh dev start --mode=http         # HTTP模式启动
  ./deploy.sh prod install-service          # 安装系统服务

环境说明:
  - dev:  源码运行，项目配置文件，开发调试
  - test: pip安装，环境变量配置，自动化测试
  - prod: 可执行文件，系统服务，生产部署
EOF
}

# 验证项目环境
validate_project_environment() {
    local env=$1
    
    log_info "验证客户端项目环境..."
    
    # 检查项目目录
    if [[ ! -f "$SERVICE_DIR/pyproject.toml" ]]; then
        log_error "pyproject.toml 不存在: $SERVICE_DIR/pyproject.toml"
        return 1
    fi
    
    log_success "项目环境验证完成"
}

# 确定部署类型
determine_deployment_type() {
    local env=$1
    
    if [[ -n "$DEPLOYMENT_TYPE" ]]; then
        log_debug "使用指定的部署类型: $DEPLOYMENT_TYPE"
        return 0
    fi
    
    case $env in
        dev)
            DEPLOYMENT_TYPE="source"
            ;;
        test)
            DEPLOYMENT_TYPE="container"
            ;;
        prod)
            DEPLOYMENT_TYPE="container"
            ;;
    esac
    
    log_debug "自动确定部署类型: $DEPLOYMENT_TYPE"
}

# 设置环境变量配置
setup_environment_config() {
    local env=$1

    log_info "设置 $env 环境变量配置..."

    case $env in
        dev)
            export ZENTAO_MCP_BACKEND_URL="http://localhost:8000"
            export ZENTAO_MCP_API_KEY="dev-api-key-for-testing"
            log_debug "开发环境变量已设置"
            ;;
        test)
            if [[ -z "${ZENTAO_MCP_BACKEND_URL:-}" ]]; then
                log_warning "请设置环境变量: ZENTAO_MCP_BACKEND_URL"
                export ZENTAO_MCP_BACKEND_URL="http://localhost:8000"
            fi
            if [[ -z "${ZENTAO_MCP_API_KEY:-}" ]]; then
                log_warning "请设置环境变量: ZENTAO_MCP_API_KEY"
                export ZENTAO_MCP_API_KEY="test-api-key-please-change"
            fi
            ;;
        prod)
            if [[ -z "${ZENTAO_MCP_BACKEND_URL:-}" ]]; then
                log_error "生产环境必须设置 ZENTAO_MCP_BACKEND_URL 环境变量"
                return 1
            fi
            if [[ -z "${ZENTAO_MCP_API_KEY:-}" ]]; then
                log_error "生产环境必须设置 ZENTAO_MCP_API_KEY 环境变量"
                return 1
            fi
            ;;
    esac

    log_success "环境变量配置完成"
}

# 源码部署
deploy_from_source() {
    local env=$1

    cd "$SERVICE_DIR"

    # 安装依赖
    execute_with_progress "安装Python依赖 (uv sync)" "uv sync" "always"

    log_success "源码部署完成"
}

# pip安装部署
deploy_with_pip() {
    local env=$1

    cd "$SERVICE_DIR"

    # 安装到系统
    execute_with_progress "安装zentao-mcp-client包 (pip)" "pip install -e ." "always"

    log_success "pip部署完成"
}

# 构建可执行文件
build_executable() {
    local env=$1

    cd "$SERVICE_DIR"

    # 检查PyInstaller
    if ! python3 -c "import PyInstaller" 2>/dev/null; then
        execute_with_progress "安装PyInstaller" "pip install pyinstaller" "always"
    fi

    # 运行构建脚本
    execute_with_progress "构建 $env 环境可执行文件" "python3 build_executable.py" "always"

    # 检查构建结果
    if [[ -f "dist/zentao-mcp-client" ]]; then
        local size_mb=$(du -m "dist/zentao-mcp-client" | cut -f1)
        log_success "可执行文件构建完成 (${size_mb}MB)"
    else
        log_error "可执行文件构建失败"
        return 1
    fi
}

# 可执行文件部署
deploy_executable() {
    local env=$1
    
    log_info "使用可执行文件部署 $env 环境..."
    
    cd "$SERVICE_DIR"
    
    # 如果可执行文件不存在，先构建
    if [[ ! -f "dist/zentao-mcp-client" ]] || [[ "$FORCE_REBUILD" == "true" ]]; then
        build_executable "$env"
    fi
    
    # 复制到系统目录
    local install_dir="/usr/local/bin"
    if [[ -w "$install_dir" ]]; then
        cp "dist/zentao-mcp-client" "$install_dir/"
        chmod +x "$install_dir/zentao-mcp-client"
        log_success "可执行文件已安装到: $install_dir/zentao-mcp-client"
    else
        log_warning "无权限安装到系统目录，请手动复制:"
        log_warning "sudo cp dist/zentao-mcp-client /usr/local/bin/"
    fi
}

# 启动服务
start_service() {
    local env=$1
    
    log_info "启动 $env 环境服务 (模式: $SERVICE_MODE)..."
    
    local start_cmd=""
    local start_args="--mode $SERVICE_MODE"
    
    if [[ "$SERVICE_MODE" == "http" ]]; then
        start_args="$start_args --host $HOST --port $PORT"
    elif [[ "$SERVICE_MODE" == "sse" ]]; then
        start_args="$start_args --port $PORT"
    fi
    
    case $DEPLOYMENT_TYPE in
        source)
            cd "$SERVICE_DIR"
            start_cmd="uv run python -m zentao_mcp_client start $start_args"
            ;;
        pip)
            start_cmd="zentao-mcp-client start $start_args"
            ;;
        executable)
            if [[ -f "/usr/local/bin/zentao-mcp-client" ]]; then
                start_cmd="/usr/local/bin/zentao-mcp-client start $start_args"
            elif [[ -f "$SERVICE_DIR/dist/zentao-mcp-client" ]]; then
                start_cmd="$SERVICE_DIR/dist/zentao-mcp-client start $start_args"
            else
                log_error "找不到zentao-mcp-client可执行文件"
                return 1
            fi
            ;;
    esac
    
    log_info "执行启动命令: $start_cmd"
    
    if [[ "$SERVICE_MODE" == "stdio" ]]; then
        log_info "STDIO模式启动，按Ctrl+C停止服务"
        eval "$start_cmd"
    else
        log_info "后台启动服务..."
        nohup $start_cmd > "/tmp/zentao-mcp-client-${env}.log" 2>&1 &
        local pid=$!
        echo $pid > "/tmp/zentao-mcp-client-${env}.pid"
        log_success "服务已启动 (PID: $pid)"
        
        if [[ "$SERVICE_MODE" == "http" ]]; then
            log_info "HTTP服务地址: http://$HOST:$PORT"
        fi
    fi
}

# 停止服务
stop_service() {
    local env=$1
    
    log_info "停止 $env 环境服务..."
    
    local pid_file="/tmp/zentao-mcp-client-${env}.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            kill "$pid"
            rm -f "$pid_file"
            log_success "服务已停止 (PID: $pid)"
        else
            log_warning "服务进程不存在 (PID: $pid)"
            rm -f "$pid_file"
        fi
    else
        log_warning "未找到PID文件，尝试查找进程..."
        pkill -f "zentao-mcp-client" || log_warning "未找到运行中的服务"
    fi
}

# 查看服务状态
show_service_status() {
    local env=$1
    
    log_info "查看 $env 环境服务状态..."
    
    local pid_file="/tmp/zentao-mcp-client-${env}.pid"
    
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log_success "服务运行中 (PID: $pid)"
            
            # 显示进程信息
            ps -p "$pid" -o pid,ppid,command 2>/dev/null || ps -p "$pid" 2>/dev/null || true
            
            # 如果是HTTP模式，测试连接
            if [[ "$SERVICE_MODE" == "http" ]]; then
                if curl -f -s "http://$HOST:$PORT/health" &> /dev/null; then
                    log_success "HTTP健康检查通过"
                else
                    log_warning "HTTP健康检查失败"
                fi
            fi
        else
            log_warning "服务进程不存在 (PID: $pid)"
            rm -f "$pid_file"
        fi
    else
        log_warning "服务未运行"
    fi
}

# 运行连接测试
run_connection_test() {
    local env=$1

    log_info "运行 $env 环境连接测试..."

    cd "$SERVICE_DIR"

    case $DEPLOYMENT_TYPE in
        source)
            if [[ -f "test_client_connect.py" ]]; then
                uv run python test_client_connect.py
            else
                log_warning "测试文件不存在: test_client_connect.py"
            fi
            ;;
        pip|executable)
            # 测试后端服务连接
            log_info "测试后端服务连接..."
            local backend_url="${ZENTAO_MCP_BACKEND_URL:-http://localhost:8000}"
            if curl -f -s "$backend_url/health" &> /dev/null; then
                log_success "后端服务连接正常"
            else
                log_error "后端服务连接失败: $backend_url"
                return 1
            fi
            ;;
    esac

    log_success "连接测试完成"
}

# 清理环境
clean_environment() {
    local env=$1

    log_warning "清理 $env 环境..."

    # 停止服务
    stop_service "$env"

    # 清理临时文件
    rm -f "/tmp/zentao-mcp-client-${env}.log"
    rm -f "/tmp/zentao-mcp-client-${env}.pid"

    case $DEPLOYMENT_TYPE in
        source)
            cd "$SERVICE_DIR"
            # 清理Python缓存
            find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
            find . -name "*.pyc" -delete 2>/dev/null || true
            # 清理构建文件
            rm -rf build/ dist/ *.egg-info/ .pytest_cache/
            ;;
        pip)
            log_info "卸载zentao-mcp-client包..."
            pip uninstall -y zentao-mcp-client || true
            ;;
        executable)
            # 删除可执行文件
            rm -f "/usr/local/bin/zentao-mcp-client"
            cd "$SERVICE_DIR"
            rm -rf dist/ build/
            ;;
    esac

    log_success "环境清理完成"
}

# 容器化部署
deploy_with_container() {
    local env=$1

    cd "$SERVICE_DIR"

    # 检查配置文件
    local compose_file="config/docker-compose.${env}.yml"
    if [[ ! -f "$compose_file" ]]; then
        log_error "Compose配置文件不存在: $compose_file"
        return 1
    fi

    # 创建网络（Podman需要）
    local network_name="zentao-${env}-network"
    ensure_network "$network_name"

    # 构建和启动服务
    local build_args=""
    if [[ "$NO_CACHE" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi

    if [[ "$PULL_LATEST" == "true" ]]; then
        build_args="$build_args --pull"
    fi

    local build_cmd="$COMPOSE_CMD -f '$compose_file' build $build_args"
    execute_with_progress "构建 $env 环境客户端容器镜像" "$build_cmd" "always"

    local deploy_cmd="$COMPOSE_CMD -f '$compose_file' up -d"
    execute_with_progress "启动 $env 环境客户端容器服务" "$deploy_cmd" "always"
}

# 容器化服务管理
manage_container_service() {
    local env=$1
    local action=$2

    cd "$SERVICE_DIR"

    local compose_file="config/docker-compose.${env}.yml"
    if [[ ! -f "$compose_file" ]]; then
        log_error "Compose配置文件不存在: $compose_file"
        return 1
    fi

    case $action in
        start)
            log_info "启动 $env 环境容器服务..."
            $COMPOSE_CMD -f "$compose_file" start
            ;;
        stop)
            log_info "停止 $env 环境容器服务..."
            $COMPOSE_CMD -f "$compose_file" stop
            ;;
        restart)
            log_info "重启 $env 环境容器服务..."
            $COMPOSE_CMD -f "$compose_file" restart
            ;;
        status)
            log_info "查看 $env 环境容器状态..."
            $COMPOSE_CMD -f "$compose_file" ps
            ;;
        logs)
            log_info "查看 $env 环境容器日志..."
            $COMPOSE_CMD -f "$compose_file" logs -f
            ;;
        clean)
            log_warning "清理 $env 环境容器..."
            if [[ "$FORCE_REBUILD" != "true" ]]; then
                echo -n "确认清理 $env 环境容器？这将删除所有容器和卷 [y/N]: "
                read -r confirm
                if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
                    log_info "取消清理操作"
                    return 0
                fi
            fi
            $COMPOSE_CMD -f "$compose_file" down --volumes --remove-orphans || true
            ;;
    esac
}

# 主函数
main() {
    log_info "Zentao MCP Client 部署脚本启动"

    # 初始化公共环境
    init_common_environment

    # 解析命令行参数
    local remaining_args=()
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                DEPLOYMENT_TYPE="$2"
                shift 2
                ;;
            --type=*)
                DEPLOYMENT_TYPE="${1#*=}"
                shift
                ;;
            -m|--mode)
                SERVICE_MODE="$2"
                shift 2
                ;;
            --mode=*)
                SERVICE_MODE="${1#*=}"
                shift
                ;;
            --host)
                HOST="$2"
                shift 2
                ;;
            --host=*)
                HOST="${1#*=}"
                shift
                ;;
            --port)
                PORT="$2"
                shift 2
                ;;
            --port=*)
                PORT="${1#*=}"
                shift
                ;;
            config|install-service)
                ACTION="$1"
                shift
                ;;
            *)
                local result
                result=$(parse_common_args "$@")
                local parse_result=$?
                
                if [[ $parse_result -eq 2 ]]; then
                    show_help
                    exit 0
                elif [[ -n "$result" ]]; then
                    remaining_args+=("$result")
                    shift
                else
                    break
                fi
                ;;
        esac
    done

    # 验证环境参数
    validate_environment "$ENVIRONMENT" || exit 1

    # 验证服务模式
    if [[ ! "$SERVICE_MODE" =~ ^(stdio|http|sse)$ ]]; then
        log_error "无效的服务模式: $SERVICE_MODE"
        log_error "支持的模式: stdio, http, sse"
        exit 1
    fi

    # 检查Python环境
    check_python_environment || exit 1

    # 验证项目环境
    validate_project_environment "$ENVIRONMENT" || exit 1

    # 确定部署类型
    determine_deployment_type "$ENVIRONMENT"

    # 验证部署类型
    if [[ ! "$DEPLOYMENT_TYPE" =~ ^(source|pip|executable|container)$ ]]; then
        log_error "无效的部署类型: $DEPLOYMENT_TYPE"
        log_error "支持的类型: source, pip, executable, container"
        exit 1
    fi

    # 对于容器部署，检测容器引擎
    if [[ "$DEPLOYMENT_TYPE" == "container" ]]; then
        detect_container_engine || exit 1
        setup_compose_command || exit 1
    fi

    log_info "使用配置:"
    log_info "  环境: $ENVIRONMENT"
    log_info "  动作: $ACTION"
    log_info "  部署类型: $DEPLOYMENT_TYPE"
    log_info "  服务模式: $SERVICE_MODE"
    if [[ "$SERVICE_MODE" != "stdio" ]]; then
        log_info "  监听地址: $HOST:$PORT"
    fi

    # 设置环境变量配置
    setup_environment_config "$ENVIRONMENT" || exit 1

    # 执行对应的动作
    case $ACTION in
        deploy)
            case $DEPLOYMENT_TYPE in
                source)
                    deploy_from_source "$ENVIRONMENT"
                    ;;
                pip)
                    deploy_with_pip "$ENVIRONMENT"
                    ;;
                executable)
                    deploy_executable "$ENVIRONMENT"
                    ;;
                container)
                    deploy_with_container "$ENVIRONMENT"
                    ;;
            esac
            log_success "部署完成"
            ;;
        build)
            if [[ "$DEPLOYMENT_TYPE" == "executable" ]]; then
                build_executable "$ENVIRONMENT"
            elif [[ "$DEPLOYMENT_TYPE" == "container" ]]; then
                manage_container_service "$ENVIRONMENT" "build"
            else
                log_warning "构建动作仅适用于executable或container部署类型"
            fi
            ;;
        start)
            if [[ "$DEPLOYMENT_TYPE" == "container" ]]; then
                manage_container_service "$ENVIRONMENT" "start"
            else
                start_service "$ENVIRONMENT"
            fi
            ;;
        stop)
            if [[ "$DEPLOYMENT_TYPE" == "container" ]]; then
                manage_container_service "$ENVIRONMENT" "stop"
            else
                stop_service "$ENVIRONMENT"
            fi
            ;;
        restart)
            if [[ "$DEPLOYMENT_TYPE" == "container" ]]; then
                manage_container_service "$ENVIRONMENT" "restart"
            else
                stop_service "$ENVIRONMENT"
                sleep 2
                start_service "$ENVIRONMENT"
            fi
            ;;
        status)
            if [[ "$DEPLOYMENT_TYPE" == "container" ]]; then
                manage_container_service "$ENVIRONMENT" "status"
            else
                show_service_status "$ENVIRONMENT"
            fi
            ;;
        logs)
            if [[ "$DEPLOYMENT_TYPE" == "container" ]]; then
                manage_container_service "$ENVIRONMENT" "logs"
            else
                log_warning "logs动作仅适用于container部署类型"
            fi
            ;;
        test)
            run_connection_test "$ENVIRONMENT"
            ;;
        clean)
            if [[ "$DEPLOYMENT_TYPE" == "container" ]]; then
                manage_container_service "$ENVIRONMENT" "clean"
            else
                clean_environment "$ENVIRONMENT"
            fi
            ;;
        *)
            log_error "动作 $ACTION 暂未实现"
            show_help
            exit 1
            ;;
    esac

    log_success "操作完成!"
}

# 执行主函数
main "$@"
