# 生产环境Dockerfile - 多阶段构建
FROM python:3.11-slim AS builder

WORKDIR /app

# 安装构建依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 安装uv和Python依赖
RUN pip install uv && uv sync --no-dev

# 复制应用代码
COPY . .

# 构建可执行文件
RUN pip install pyinstaller && \
    python build_executable.py

# 生产阶段
FROM python:3.11-slim

WORKDIR /app

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && groupadd -r zentao && useradd -r -g zentao zentao

# 复制可执行文件
COPY --from=builder /app/dist/zentao-mcp-client /usr/local/bin/zentao-mcp-client
RUN chmod +x /usr/local/bin/zentao-mcp-client

# 创建必要目录
RUN mkdir -p logs && chown -R zentao:zentao logs

# 切换到非root用户
USER zentao

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动命令
CMD ["zentao-mcp-client", "start", "--mode", "http", "--host", "0.0.0.0", "--port", "8080"]
