# 镜像源配置文件
# 用于优化国内网络环境下的构建速度

# ============================================================================
# 注意: Docker镜像源已在系统级别配置，此处不再配置
# ============================================================================

# ============================================================================
# 包管理器镜像源配置
# ============================================================================

# Python pip镜像源
PIP_INDEX_URL=${PIP_INDEX_URL:-https://pypi.tuna.tsinghua.edu.cn/simple}
PIP_TRUSTED_HOST=${PIP_TRUSTED_HOST:-pypi.tuna.tsinghua.edu.cn}

# 备选pip源
# PIP_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
# PIP_INDEX_URL=https://pypi.mirrors.ustc.edu.cn/simple/

# Node.js npm镜像源
NPM_REGISTRY=${NPM_REGISTRY:-https://registry.npmmirror.com}

# 备选npm源
# NPM_REGISTRY=https://mirrors.cloud.tencent.com/npm/
# NPM_REGISTRY=https://mirrors.huaweicloud.com/repository/npm/

# Alpine apk镜像源
APK_MIRROR=${APK_MIRROR:-mirrors.aliyun.com}

# 备选apk源
# APK_MIRROR=mirrors.tuna.tsinghua.edu.cn
# APK_MIRROR=mirrors.ustc.edu.cn

# ============================================================================
# 构建优化配置
# ============================================================================

# 是否启用镜像加速
ENABLE_MIRROR_ACCELERATION=${ENABLE_MIRROR_ACCELERATION:-true}

# 构建并行度
BUILD_PARALLEL=${BUILD_PARALLEL:-$(nproc)}

# 是否使用构建缓存
USE_BUILD_CACHE=${USE_BUILD_CACHE:-true}

# ============================================================================
# 地区特定配置
# ============================================================================

# 时区设置
TIMEZONE=${TIMEZONE:-Asia/Shanghai}

# 语言环境
LANG=${LANG:-zh_CN.UTF-8}
LC_ALL=${LC_ALL:-zh_CN.UTF-8}
